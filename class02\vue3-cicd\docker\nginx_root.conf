pcre_jit on;


events {
    worker_connections 1024;
}

env POD_NAMESPACE;

http {
    include mime.types;
    default_type application/octet-stream;

    log_format nginxlog_json escape=json '{ "timestamp": "$time_iso8601", '
    '"remote_addr": "$remote_addr", '
     '"body_bytes_sent": $body_bytes_sent, '
     '"request_time": $request_time, '
     '"response_status": $status, '
     '"request": "$request", '
     '"api": "$request_uri", '
     '"request_method": "$request_method", '
     '"host": "$host",'
     '"upstream_addr": "$upstream_addr",'
     '"http_x_forwarded_for": "$http_x_forwarded_for",'
     '"http_x_real_ip": "$http_x_real_ip",'
     '"http_referrer": "$http_referer", '
     '"http_user_agent": "$http_user_agent", '
     '"http_version": "$server_protocol", '
     '"nginx_access": true }';

    access_log  /usr/local/openresty/nginx/logs/access_format.log  nginxlog_json;

    error_log /dev/stdout notice;
    error_log /dev/stdout warn;
    error_log /dev/stdout error;

    # rewrite_log on;

    # See Move default writable paths to a dedicated directory (#119)
    # https://github.com/openresty/docker-openresty/issues/119
    client_body_temp_path /var/run/openresty/nginx-client-body;
    proxy_temp_path /var/run/openresty/nginx-proxy;
    fastcgi_temp_path /var/run/openresty/nginx-fastcgi;
    uwsgi_temp_path /var/run/openresty/nginx-uwsgi;
    scgi_temp_path /var/run/openresty/nginx-scgi;

    sendfile on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout 65;

    #gzip  on;
    include /usr/local/openresty/nginx/conf/vhost/*.conf;
}
